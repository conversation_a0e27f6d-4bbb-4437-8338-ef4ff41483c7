import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Navigation from './components/Navigation'
import Presentation from './components/Presentation'
import SearchableNetworks from './components/SearchableNetworks'
import StationsInfo from './components/StationsInfo'
import AddNetwork from './components/AddNetwork'
import NetworksVisualization from './components/NetworksVisualization'
import './App.css'

function App() {
  return (
    <Router>
      <div className="app">
        <header className="app-header">
          <h1>🚴 Gestionnaire de Réseaux de Vélos</h1>
          <p>Application de gestion des réseaux de vélos en libre-service</p>
        </header>

        <Navigation />

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Presentation />} />
            <Route path="/presentation" element={<Presentation />} />
            <Route path="/networks" element={<SearchableNetworks />} />
            <Route path="/stations" element={<StationsInfo />} />
            <Route path="/add" element={<AddNetwork />} />
            <Route path="/visualization" element={<NetworksVisualization />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
