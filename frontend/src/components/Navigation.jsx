import { Link, useLocation } from 'react-router-dom'
import './Navigation.css'

function Navigation() {
  const location = useLocation()

  const navItems = [
    { path: '/presentation', label: 'Présentation', icon: '🏠' },
    { path: '/networks', label: '<PERSON><PERSON>eaux', icon: '🔍' },
    { path: '/stations', label: 'Stations', icon: '📊' },
    { path: '/add', label: 'Ajouter', icon: '➕' },
    { path: '/visualization', label: 'Visualisation', icon: '📈' }
  ]

  return (
    <nav className="navigation">
      <div className="nav-container">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}
          >
            <span className="nav-icon">{item.icon}</span>
            <span className="nav-label">{item.label}</span>
          </Link>
        ))}
      </div>
    </nav>
  )
}

export default Navigation
