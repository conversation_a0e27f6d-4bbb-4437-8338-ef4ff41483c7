.presentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  color: white;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 40px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.1rem;
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
}

/* Features Overview */
.features-overview {
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.feature-icon {
  font-size: 2.5rem;
  margin-right: 15px;
}

.feature-header h3 {
  font-size: 1.5rem;
  color: #2d3748;
  margin: 0;
}

.feature-description {
  font-size: 1.1rem;
  color: #4a5568;
  margin-bottom: 25px;
  line-height: 1.6;
}

.feature-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 20px;
}

.feature-capabilities h4,
.feature-lists h4,
.feature-tabs h4 {
  font-size: 1rem;
  color: #2d3748;
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-capabilities ul {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.feature-capabilities li {
  padding: 8px 0;
  color: #4a5568;
  position: relative;
  padding-left: 20px;
}

.feature-capabilities li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #48bb78;
  font-weight: bold;
}

.list-item,
.tab-item {
  margin-bottom: 12px;
  padding: 10px;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.list-item strong,
.tab-item strong {
  color: #2d3748;
  display: block;
  margin-bottom: 4px;
}

.tab-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.tab-icon {
  font-size: 1.2rem;
  margin-top: 2px;
}

.tab-info {
  flex: 1;
}

/* Navigation Guide */
.navigation-guide {
  margin-bottom: 60px;
}

.guide-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 40px;
}

.step-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.step-number {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 1.3rem;
  color: #2d3748;
  margin-bottom: 10px;
}

.step-content p {
  color: #4a5568;
  line-height: 1.6;
}

/* Technical Info */
.technical-info {
  margin-bottom: 40px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 40px;
}

.tech-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.tech-card h3 {
  font-size: 1.3rem;
  color: #2d3748;
  margin-bottom: 15px;
}

.tech-card ul {
  list-style: none;
  padding: 0;
}

.tech-card li {
  padding: 6px 0;
  color: #4a5568;
  position: relative;
  padding-left: 20px;
}

.tech-card li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .presentation {
    padding: 15px;
  }
  
  .hero-content h1 {
    font-size: 2.2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-stats {
    gap: 20px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 20px;
  }
  
  .step-card {
    flex-direction: column;
    text-align: center;
  }
  
  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 40px 20px;
  }
  
  .hero-content h1 {
    font-size: 1.8rem;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 15px;
  }
}
