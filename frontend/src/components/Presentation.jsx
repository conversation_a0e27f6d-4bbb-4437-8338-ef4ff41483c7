import React from 'react'
import './Presentation.css'

function Presentation() {
  const features = [
    {
      id: 'networks',
      title: '🔍 Réseaux de Vélos',
      icon: '🔍',
      description: 'Explorez et recherchez tous les réseaux de vélos en libre-service',
      features: [
        'Liste complète de tous les réseaux mondiaux',
        'Filtrage par pays en temps réel',
        'Tableau détaillé avec nom, entreprise, ville et pays',
        'Compteur dynamique des résultats filtrés',
        'Interface de recherche intuitive'
      ],
      lists: [
        {
          name: 'Liste des Réseaux',
          description: 'Tableau complet affichant tous les réseaux avec leurs informations détaillées'
        }
      ]
    },
    {
      id: 'stations',
      title: '📊 Informations sur les Stations',
      icon: '📊',
      description: 'Découvrez le nombre de stations par pays avec deux modes de visualisation',
      features: [
        'Deux modes de visualisation : Liste et Carte',
        'Sélection interactive des pays',
        'Comptage automatique des stations par pays',
        'Carte interactive avec marqueurs géolocalisés',
        'Interface à onglets pour une navigation fluide'
      ],
      lists: [
        {
          name: 'Liste des Pays',
          description: 'Liste cliquable de tous les pays avec des réseaux de vélos'
        },
        {
          name: 'Informations par Pays',
          description: 'Affichage du nombre total de stations pour le pays sélectionné'
        }
      ],
      tabs: [
        {
          name: 'Liste',
          icon: '📋',
          description: 'Mode liste avec sélection de pays et affichage des statistiques'
        },
        {
          name: 'Carte',
          icon: '🗺️',
          description: 'Carte interactive mondiale avec marqueurs par pays'
        }
      ]
    },
    {
      id: 'add',
      title: '➕ Ajouter un Réseau',
      icon: '➕',
      description: 'Créez et ajoutez de nouveaux réseaux à la base de données',
      features: [
        'Formulaire de création complet',
        'Validation des champs obligatoires',
        'Ajout en temps réel à la base de données',
        'Messages de confirmation et d\'erreur',
        'Interface utilisateur intuitive'
      ],
      lists: [
        {
          name: 'Formulaire de Création',
          description: 'Champs pour nom, entreprise, ville et pays du nouveau réseau'
        },
        {
          name: 'Informations d\'Aide',
          description: 'Guide et conseils pour remplir correctement le formulaire'
        }
      ]
    },
    {
      id: 'visualization',
      title: '📈 Visualisation des Réseaux',
      icon: '📈',
      description: 'Analysez la distribution des réseaux avec des graphiques interactifs',
      features: [
        'Trois types de visualisations différentes',
        'Graphiques en barres et en secteurs',
        'Données en temps réel',
        'Interface interactive avec boutons de sélection',
        'Analyses statistiques détaillées'
      ],
      lists: [
        {
          name: 'Distribution par Pays',
          description: 'Graphique en barres montrant le nombre de réseaux par pays'
        },
        {
          name: 'Distribution par Ville',
          description: 'Graphique en barres des villes avec le plus de réseaux'
        },
        {
          name: 'Répartition par Entreprise',
          description: 'Graphique en secteurs des entreprises gérant les réseaux'
        }
      ],
      tabs: [
        {
          name: 'Par Pays',
          icon: '🌍',
          description: 'Visualisation de la distribution géographique des réseaux'
        },
        {
          name: 'Par Ville',
          icon: '🏙️',
          description: 'Analyse des villes avec le plus grand nombre de réseaux'
        },
        {
          name: 'Par Entreprise',
          icon: '🏢',
          description: 'Répartition des réseaux selon les entreprises gestionnaires'
        }
      ]
    }
  ]

  return (
    <div className="presentation">
      <div className="hero-section">
        <div className="hero-content">
          <h1>🚴 Gestionnaire de Réseaux de Vélos</h1>
          <p className="hero-subtitle">
            Application complète de gestion et d'analyse des réseaux de vélos en libre-service
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">4</span>
              <span className="stat-label">Modules</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">Global</span>
              <span className="stat-label">Couverture</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">Temps Réel</span>
              <span className="stat-label">Données</span>
            </div>
          </div>
        </div>
      </div>

      <div className="features-overview">
        <div className="section-header">
          <h2>🎯 Fonctionnalités Principales</h2>
          <p>Découvrez tous les outils disponibles pour gérer et analyser les réseaux de vélos</p>
        </div>

        <div className="features-grid">
          {features.map((feature) => (
            <div key={feature.id} className="feature-card">
              <div className="feature-header">
                <div className="feature-icon">{feature.icon}</div>
                <h3>{feature.title}</h3>
              </div>
              
              <p className="feature-description">{feature.description}</p>
              
              <div className="feature-details">
                <div className="feature-capabilities">
                  <h4>✨ Capacités</h4>
                  <ul>
                    {feature.features.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                {feature.lists && (
                  <div className="feature-lists">
                    <h4>📋 Listes et Données</h4>
                    {feature.lists.map((list, index) => (
                      <div key={index} className="list-item">
                        <strong>{list.name}:</strong>
                        <span>{list.description}</span>
                      </div>
                    ))}
                  </div>
                )}

                {feature.tabs && (
                  <div className="feature-tabs">
                    <h4>🗂️ Onglets Disponibles</h4>
                    {feature.tabs.map((tab, index) => (
                      <div key={index} className="tab-item">
                        <span className="tab-icon">{tab.icon}</span>
                        <div className="tab-info">
                          <strong>{tab.name}:</strong>
                          <span>{tab.description}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="navigation-guide">
        <div className="section-header">
          <h2>🧭 Guide de Navigation</h2>
          <p>Comment utiliser efficacement chaque section de l'application</p>
        </div>

        <div className="guide-steps">
          <div className="step-card">
            <div className="step-number">1</div>
            <div className="step-content">
              <h3>🔍 Explorez les Réseaux</h3>
              <p>Commencez par l'onglet "Réseaux" pour découvrir tous les réseaux disponibles. Utilisez le filtre par pays pour affiner votre recherche.</p>
            </div>
          </div>

          <div className="step-card">
            <div className="step-number">2</div>
            <div className="step-content">
              <h3>📊 Analysez les Stations</h3>
              <p>Visitez l'onglet "Stations" pour voir le nombre de stations par pays. Basculez entre la vue liste et carte selon vos préférences.</p>
            </div>
          </div>

          <div className="step-card">
            <div className="step-number">3</div>
            <div className="step-content">
              <h3>➕ Contribuez aux Données</h3>
              <p>Utilisez l'onglet "Ajouter" pour enrichir la base de données avec de nouveaux réseaux que vous connaissez.</p>
            </div>
          </div>

          <div className="step-card">
            <div className="step-number">4</div>
            <div className="step-content">
              <h3>📈 Visualisez les Tendances</h3>
              <p>Terminez par l'onglet "Visualisation" pour comprendre les patterns et distributions à travers des graphiques interactifs.</p>
            </div>
          </div>
        </div>
      </div>

      <div className="technical-info">
        <div className="section-header">
          <h2>⚙️ Informations Techniques</h2>
        </div>
        
        <div className="tech-grid">
          <div className="tech-card">
            <h3>🎨 Interface</h3>
            <ul>
              <li>React avec navigation par onglets</li>
              <li>Design responsive et moderne</li>
              <li>Interactions en temps réel</li>
              <li>Feedback utilisateur immédiat</li>
            </ul>
          </div>
          
          <div className="tech-card">
            <h3>📊 Données</h3>
            <ul>
              <li>Base de données SQLite</li>
              <li>API REST Python/Flask</li>
              <li>Synchronisation automatique</li>
              <li>Validation des entrées</li>
            </ul>
          </div>
          
          <div className="tech-card">
            <h3>🗺️ Cartographie</h3>
            <ul>
              <li>Cartes interactives Leaflet</li>
              <li>Géolocalisation automatique</li>
              <li>Marqueurs informatifs</li>
              <li>Navigation fluide</li>
            </ul>
          </div>
          
          <div className="tech-card">
            <h3>📈 Visualisations</h3>
            <ul>
              <li>Graphiques Chart.js</li>
              <li>Analyses statistiques</li>
              <li>Données dynamiques</li>
              <li>Export possible</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Presentation
